{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "Flux Studio", "logo": {"src": "/logo.png", "alt": "Flux Studio"}, "url": "/"}, "nav": {"items": [{"title": "How It Works", "url": "/#feature", "icon": "RiSparkling2Line"}, {"title": "Pricing", "url": "/pricing", "icon": "RiMoneyDollarCircleLine"}, {"title": "Gallery", "url": "/showcase", "icon": "RiApps2Line"}, {"title": "Guides", "url": "/guides", "icon": "RiBookOpenLine"}]}, "buttons": [{"title": "Try Now", "url": "/#tool", "target": "_self", "variant": "link", "icon": "RiArrowRightUpLine"}], "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "Transform Your Product Photos with Flux Kontext for E-commerce", "highlight_text": "Flux Kontext", "description": "Upload your reference photo and garment to generate infinite professional e-commerce shots in seconds.<br/>From 1 successful image to 100 variations with perfect consistency.", "announcement": {"label": "NEW", "title": "🚀 AI Visual Solution Amplifier", "url": "/#tool"}, "buttons": [{"title": "Try Now", "icon": "RiCameraFill", "url": "/#tool", "target": "_self", "variant": "default"}, {"title": "View Gallery", "icon": "RiGalleryFill", "url": "/showcase", "target": "_self", "variant": "outline"}], "show_happy_users": false, "show_badge": false}, "branding": {"title": "Powered by cutting-edge AI technology", "items": [{"title": "Flux Kontext", "image": {"src": "/imgs/logos/flux.svg", "alt": "Flux Kontext"}}, {"title": "AI Vision", "image": {"src": "/imgs/logos/ai-vision.svg", "alt": "AI Vision"}}, {"title": "Deep Learning", "image": {"src": "/imgs/logos/deep-learning.svg", "alt": "Deep Learning"}}, {"title": "Computer Vision", "image": {"src": "/imgs/logos/computer-vision.svg", "alt": "Computer Vision"}}, {"title": "Neural Networks", "image": {"src": "/imgs/logos/neural-networks.svg", "alt": "Neural Networks"}}]}, "introduce": {"name": "introduce", "title": "What is Flux Kontext for E-commerce", "label": "Introduction", "description": "Flux Kontext is an AI visual solution amplifier that transforms your successful product photos into infinite variations with perfect consistency. From 1 to 100 in seconds.", "image": {"src": "/imgs/features/flux-kontext-demo.png"}, "items": [{"title": "Reference-Based Generation", "description": "Upload your best-performing product photo as a reference to maintain visual consistency across all variations.", "icon": "RiImageFill"}, {"title": "Garment Replacement", "description": "Simply upload a new garment image and watch as AI seamlessly replaces it while preserving model, pose, and lighting.", "icon": "RiTShirtFill"}, {"title": "Instant Results", "description": "Generate professional e-commerce photos in seconds, not hours. Perfect for scaling your product catalog.", "icon": "RiFlashlightFill"}]}, "benefit": {"name": "benefit", "title": "Why Choose Flux Kontext", "label": "Benefits", "description": "Experience the power of AI-driven visual consistency for your e-commerce business.", "items": [{"title": "Perfect Consistency", "description": "Maintain exact visual standards across all product variations. Same model, pose, lighting, and style every time.", "icon": "RiCheckboxCircleFill", "image": {"src": "/imgs/features/consistency.png"}}, {"title": "Scalable Production", "description": "Transform one successful photo into hundreds of variations. Scale your product catalog without expensive photoshoots.", "icon": "RiStackFill", "image": {"src": "/imgs/features/scalable.png"}}, {"title": "Cost Effective", "description": "Reduce photography costs by 90%. No more model bookings, studio rentals, or lengthy production cycles.", "icon": "RiMoneyDollarCircleFill", "image": {"src": "/imgs/features/cost-effective.png"}}]}, "usage": {"name": "usage", "title": "How Flux Kontext Works", "description": "Transform your product photography in three simple steps:", "image": {"src": "/imgs/features/flux-workflow.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "Upload Reference Image", "description": "Upload your best-performing product photo that defines your visual standard - model, pose, lighting, and style.", "image": {"src": "/imgs/features/upload-reference.png"}}, {"title": "Upload Garment Image", "description": "Upload a flat lay or product image of the new garment you want to showcase on your model.", "image": {"src": "/imgs/features/upload-garment.png"}}, {"title": "Generate & Download", "description": "AI seamlessly replaces the garment while maintaining perfect consistency with your reference image.", "image": {"src": "/imgs/features/generate-result.png"}}]}, "feature": {"name": "feature", "title": "Advanced Features of Flux Kontext", "description": "Professional-grade AI technology designed specifically for e-commerce visual consistency.", "items": [{"title": "Visual Consistency Engine", "description": "Maintains exact model pose, lighting, and style across all generated variations.", "icon": "RiEyeFill"}, {"title": "Intelligent Garment Mapping", "description": "Advanced AI recognizes garment boundaries and seamlessly replaces clothing items.", "icon": "RiTShirtFill"}, {"title": "High-Resolution Output", "description": "Generate professional 4K images suitable for e-commerce platforms and print media.", "icon": "RiHdFill"}, {"title": "Batch Processing", "description": "Process multiple garments simultaneously to scale your product catalog efficiently.", "icon": "RiStackFill"}, {"title": "Style Preservation", "description": "Maintains brand aesthetic, color grading, and photographic style across all outputs.", "icon": "RiPaletteFill"}, {"title": "API Integration", "description": "Seamlessly integrate with your existing e-commerce workflow via REST API.", "icon": "RiCodeSSlashFill"}]}, "stats": {"name": "stats", "label": "Performance", "title": "Proven Results with Flux Kontext", "description": "Trusted by e-commerce brands worldwide for consistent, high-quality results.", "icon": "FaRegHeart", "items": [{"title": "Cost Reduction", "label": "90%", "description": "Photography Costs"}, {"title": "Generation Speed", "label": "30", "description": "Seconds Average"}, {"title": "Visual Consistency", "label": "99.5%", "description": "Accuracy Rate"}]}, "testimonial": {"name": "testimonial", "label": "Testimonial", "title": "What E-commerce Brands Say About Flux Kontext", "description": "Hear from fashion retailers and e-commerce businesses transforming their product photography.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Founder of StyleVault", "description": "Flux Kontext transformed our product photography workflow. We went from 2-week photoshoots to generating 100+ product images in a single day with perfect consistency.", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "Creative Director at FashionForward", "description": "The visual consistency is incredible. Every generated image maintains our exact brand aesthetic - same lighting, pose, and style. Our conversion rates increased by 35%.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "E-commerce Manager", "description": "As a small business, we couldn't afford constant photoshoots. Flux Kontext lets us showcase our entire inventory professionally without breaking the budget.", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON>", "label": "Brand Manager at TrendyWear", "description": "We reduced our photography budget by 80% while increasing our product catalog by 300%. Flux Kontext pays for itself within the first month.", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "Founder of EcoFashion", "description": "The AI understands fashion perfectly. It maintains fabric textures, fit, and draping naturally. Our customers can't tell the difference from traditional photography.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Digital Marketing Director", "description": "Flux Kontext revolutionized our A/B testing. We can now test dozens of product variations instantly to optimize conversion rates.", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About Flux Kontext", "description": "Have another question? Contact our support team for assistance.", "items": [{"title": "What exactly is Flux Kontext and how does it work?", "description": "Flux Kontext is an AI visual solution amplifier that transforms your successful product photos into infinite variations. You upload a reference image and a garment, and our AI seamlessly replaces the clothing while maintaining perfect visual consistency."}, {"title": "Do I need design skills to use Flux Kontext?", "description": "Not at all! Flux Kontext is designed for e-commerce businesses of all sizes. Simply upload your images and let our AI handle the complex visual processing. No design experience required."}, {"title": "What types of garments work best with Flux Kontext?", "description": "Flux Kontext works with all types of clothing - from casual wear to formal attire, accessories, and footwear. The AI is trained specifically on fashion items and understands fabric textures, fit, and draping."}, {"title": "How long does it take to generate a new product image?", "description": "Most images are generated within 30 seconds. Complex garments or high-resolution outputs may take up to 2 minutes. Our AI processes images in real-time for immediate results."}, {"title": "What image quality and formats are supported?", "description": "We support JPG, PNG, and WebP formats. Input images should be at least 512x512 pixels for best results. Output images are generated in high resolution (up to 4K) suitable for e-commerce platforms."}, {"title": "Can I maintain my brand's visual style across all images?", "description": "Absolutely! That's the core strength of Flux Kontext. By using your best-performing image as a reference, all generated variations maintain your exact brand aesthetic, lighting, and photographic style."}]}, "cta": {"name": "cta", "title": "Transform Your E-commerce Photography Today", "description": "Start generating professional product images with Flux Kontext.", "buttons": [{"title": "Try Free Now", "url": "/#tool", "target": "_self", "icon": "GoArrowUpRight"}, {"title": "View Pricing", "url": "/pricing", "target": "_self", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "Flux Studio", "description": "Flux Kontext for E-commerce - Transform your product photography with AI visual solution amplifier. From 1 successful image to 100 variations.", "logo": {"src": "/logo.png", "alt": "Flux Studio"}, "url": "/"}, "copyright": "© 2025 • Flux Studio All rights reserved.", "nav": {"items": [{"title": "About", "children": [{"title": "Features", "url": "/#feature", "target": "_self"}, {"title": "Showcases", "url": "/#showcase", "target": "_self"}, {"title": "Pricing", "url": "/#pricing", "target": "_self"}]}, {"title": "Resources", "children": [{"title": "Documents", "url": "https://docs.shipany.ai", "target": "_blank"}, {"title": "Components", "url": "https://shipany.ai/components", "target": "_blank"}, {"title": "Templates", "url": "https://shipany.ai/templates", "target": "_blank"}]}, {"title": "Friends", "children": [{"title": "ThinkAny", "url": "https://thinkany.ai", "target": "_blank"}, {"title": "HeyBeauty", "url": "https://heybeauty.ai", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://pagen.so", "target": "_blank"}]}]}, "social": {"items": [{"title": "X", "icon": "RiTwitterXFill", "url": "https://x.com/shipanyai", "target": "_blank"}, {"title": "<PERSON><PERSON><PERSON>", "icon": "RiGithubFill", "url": "https://github.com/shipanyai", "target": "_blank"}, {"title": "Discord", "icon": "RiDiscordFill", "url": "https://discord.gg/HQNnrzjZQS", "target": "_blank"}, {"title": "Email", "icon": "RiMailLine", "url": "mailto:<EMAIL>", "target": "_self"}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}